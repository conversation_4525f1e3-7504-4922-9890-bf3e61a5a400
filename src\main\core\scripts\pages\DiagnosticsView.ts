import { invoke } from "@tauri-apps/api/core";
import { listen } from '@tauri-apps/api/event';
import { BatteryData } from "../interfaces/batteryData";
import { resolveResource } from '@tauri-apps/api/path';
import { convertFileSrc } from '@tauri-apps/api/core';
import { getBatteryType } from "../../dataContainer/BatteryDataContainer";

console.log("DiagnosticsView.ts 已載入");


let tbxProduct;
let tbxFirmwareVersion;
let tbxSerialNumber;

// 全域變數儲存電池資料
let currentBatteryData: BatteryData[] = [];
var timeoutID = -1;

async function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function insertBattery(num: number, batteryData?: BatteryData) {
    const battery = document.getElementById(`battery${num}`);
    const slot = document.getElementById(`slot${num}`);
    // const info = document.getElementById(`info${num}`);
    // const card = document.createElement('div');
    
    if(!battery || !slot) return;
    if(!batteryData) return;

    // 移除所有電池狀態 class
    battery.classList.remove('battery-normal-image', 'battery-normal-image-rotate-180', 'battery-charger-image', 'battery-charger-image-rotate-180', 'battery-low-image', 'battery-low-image-rotate-180', 'battery-broken-image', 'battery-broken-image-rotate-180');

    let batteryClass = 'battery-normal-image';
    if (batteryData.current > 0) {
        batteryClass = 'battery-charger-image';
    } else if (batteryData.relativeStateOfCharged < 20) {
        batteryClass = 'battery-low-image';
    } else if (getBatteryHealth(batteryData?.fullyChargedCapacity ?? 0, batteryData?.designCapacity ?? 0) < 50) {
        batteryClass = 'battery-broken-image';
    }

    if (num === 0 || num === 2 || num === 4) {
        batteryClass += '-rotate-180';
    }

    battery.classList.add(batteryClass);
    
    // const batteryHealth = getBatteryHealth(batteryData?.fullyChargedCapacity ?? 0, batteryData?.designCapacity ?? 0);
    // if(isBatteryComplete(batteryData?.fullyChargedCapacity ?? 0, batteryData?.remainingCapacity ?? 0)){
    //   card.innerHTML = `
    //     <div class="battery-info" id="info${num}">
    //         <p>Serial: ${batteryData?.sn}</p>
    //         <p>Current: ${batteryData?.current} mA</p>
    //         <p>Voltage: ${batteryData?.voltage} mV</p>
    //         <p>Temp: ${batteryData?.temperature.toFixed(2)} °C</p>
    //         <p>Health: ${batteryHealth.toFixed(2)}%</p>
    //         <p>Cycle: ${batteryData?.cycle}</p>
    //     </div>
    //     <button class="details-btn" onclick="showDetails(${batteryData?.id})">Details</button>
    //   `;
    // }else{
    //   card.innerHTML = `
    //     <div class="battery-info" id="info${num}">
    //         <p>Serial: ${batteryData?.sn}</p>
    //     </div>
    //   `;
    // }
    
    // 清空現有內容
    battery.innerHTML = '';
    battery.appendChild(await createBatteryCard(num, batteryData));
    battery.style.visibility = 'visible';
    battery.classList.remove('out');
    battery.classList.add('in');
    slot.style.display = 'none';
}

function removeBattery(num: number) {
    const battery = document.getElementById(`battery${num}`);
    const slot = document.getElementById(`slot${num}`);

    if (!battery || !slot) return;

    battery.classList.remove('in');
    battery.classList.add('out');
    slot.style.display = 'block';
    // 等待動畫完成後隱藏（1秒 = transition 時間）
    setTimeout(() => {
        battery.style.visibility = 'hidden';
    }, 600);
}

async function showBatteries() {
  try {
    console.log("開始獲取電池資料...");
    /// 調用 Charger_GetBatteries 並儲存回傳資料
    const batteryDataList: BatteryData[] = await invoke<BatteryData[]>("Charger_GetBatteries");
    /// 顯示資料
    console.log("Battery Data:", batteryDataList);
    
    // 更新電池卡片顯示
    updateBatteryDisplay(batteryDataList);

    /// 逐項顯示每個電池的資料（保留舊的動畫效果，如果需要的話）
    // batteryDataList.forEach((data, index) => {
    //   console.log(`Battery ${index + 1}: ID=${data.id}, SN=${data.sn}`);
    //   if(data.sn !== "") {
    //     insertBattery(index + 1);
    //   }else{
    //     removeBattery(index + 1);
    //   }
    // });
    console.log("電池資料獲取完成");
  } catch (error) {
      console.error("獲取電池資料時發生錯誤:", error);
      // 停止定時器以防止持續錯誤
      if (timeoutID !== -1) {
        window.clearInterval(timeoutID);
        timeoutID = -1;
        console.log("已停止定時器以防止持續錯誤");
      }
  }
}

async function Initial() {
  (document.getElementsByClassName("text-loading")[0] as HTMLElement).style.display = 'block';
  try {
    console.log("開始初始化...");
    
    // 清除現有的定時器
    if (timeoutID !== -1) {
      //window.clearInterval(timeoutID);
      timeoutID = -1;
      console.log("已清除現有定時器");
    }
    
    const vList = [0x0000FFFF, 0xFFFF0000, 0x03EB4736];
    for (const product of vList) {
      console.log(`嘗試初始化產品: 0x${product.toString(16)}`);
      
      if (await invoke("Initial", { product })) {
        console.log(`產品 0x${product.toString(16)} 初始化成功，等待 5 秒...`);
        await delay(300);
        
        tbxProduct = await invoke("Charger_GetName");
        tbxFirmwareVersion = await invoke("Charger_GetVersion");
        tbxSerialNumber = await invoke("Charger_GetSerialNumber");
        
        console.log("Product:", tbxProduct, "Firmware:", tbxFirmwareVersion, "Serial:", tbxSerialNumber);
        
        // 先執行一次電池資料獲取測試
        console.log("執行初始電池資料獲取測試...");
        await showBatteries();
        
        // 如果測試成功，才開始定時器
        console.log("開始設定定時器，每 6 秒獲取一次電池資料");
        // timeoutID = window.setInterval(() => showBatteries(), 6000);
        
        console.log("初始化完成");
        (document.getElementsByClassName("text-loading")[0] as HTMLElement).style.display = 'none';
        return;
      }
    }
    (document.getElementsByClassName("text-loading")[0] as HTMLElement).style.display = 'none';
    console.log("所有產品初始化失敗");
    tbxProduct = "NA";
    tbxFirmwareVersion = "NA";
    tbxSerialNumber = "NA";
  } catch (error) {
    console.error("初始化過程中發生錯誤:", error);
    // 確保清除定時器
    if (timeoutID !== -1) {
      window.clearInterval(timeoutID);
      timeoutID = -1;
    }
    tbxProduct = "ERROR";
    tbxFirmwareVersion = "ERROR";
    tbxSerialNumber = "ERROR";
  }

  
}

// 更新電池顯示
function updateBatteryDisplay(batteryDataList: BatteryData[]) {
  currentBatteryData = batteryDataList;
  console.log("batteryDataList Data:", batteryDataList);
  console.log("currentBatteryData Data:", currentBatteryData);
  
  // 電池排列順序：5 3 1 / 4 2 0
  const batteryOrder = [5, 3, 1, 4, 2, 0];

  // 根據指定順序創建電池卡片
  batteryOrder.forEach(index => {
    const batteryData = batteryDataList.find(battery => battery.id === index);
    console.log(`Battery ${index}: ID=${batteryData?.id}, SN=${batteryData?.sn}`);
    if(batteryData && batteryData?.sn !== "") {
      console.log(`insertBattery ${index}: ID=${batteryData?.id}, SN=${batteryData?.sn}`);
      insertBattery(index, batteryData);
    }else{
      console.log(`removeBattery ${index}`);
      removeBattery(index);
    }

    //const batteryCard = createBatteryCard(index, batteryData);
    //batteryGrid.appendChild(batteryCard);
  });

  // 更新統計資訊
  updateStatistics(batteryDataList);
}

function getBatteryHealth(m_FullChargeCapacity: number, m_DesignCapacity: number): number
{
    try
    {
        const fHealth = (m_FullChargeCapacity / m_DesignCapacity) * 100;

        return (fHealth > 100) ? 100 : fHealth;
    }
    catch (error)
    {
        console.error("計算電池健康狀況時發生錯誤:", error);
        return 0;
    }
}

function isBatteryComplete(m_fullyChargedCapacity: number, m_remainingCapacity: number): boolean
{
    try
    {
      if(m_fullyChargedCapacity !== 0 && m_remainingCapacity !== 0){
        return true;  
      }
    }
    catch (error)
    {
        console.error("計算電池健康狀況時發生錯誤:", error);
        return false;
    }
    return false;
}

// 創建電池卡片
async function createBatteryCard(slotId: number, batteryData?: BatteryData): Promise<HTMLElement> {
  const card = document.createElement('div');
  card.className = `battery-card ${!batteryData || batteryData.sn === '' ? 'no-battery' : ''}`;
  // console.log("batteryData Data:", batteryData);
  // if (batteryData && batteryData.sn !== '') {
  //   // 有電池的情況
  //   const batteryLevel = (batteryData && (batteryData.relativeStateOfCharged !== undefined || batteryData.relativeStateOfCharged !== undefined)) ? (batteryData.relativeStateOfCharged || batteryData.relativeStateOfCharged) : 0;
  //   const levelClass = batteryLevel > 60 ? 'high' : batteryLevel > 30 ? 'medium' : 'low';
  //   console.log("batteryData.relativeStateOfCharged :", batteryData?.relativeStateOfCharged );
  //   console.log("batteryLevel:", batteryLevel);
  //   card.innerHTML = `
  //     <div class="battery-icon">
  //       <div class="battery-level ${levelClass}" style="height: ${batteryLevel}%"></div>
  //       <div class="battery-percentage" style="color: ${batteryLevel > 60 ? '#10b981' : batteryLevel > 30 ? '#f59e0b' : '#ef4444'}; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 0.8em; font-weight: bold; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);">${batteryLevel}%</div>
  //     </div>
  //     <div class="serial-number">${batteryData.sn}</div>
  //     <div class="battery-info-grid">
  //       <div class="info-item">
  //         <div class="info-label">Current</div>
  //         <div class="info-value">${batteryData.current} mA</div>
  //       </div>
  //       <div class="info-item">
  //         <div class="info-label">Voltage</div>
  //         <div class="info-value">${batteryData.voltage} mV</div>
  //       </div>
  //       <div class="info-item">
  //         <div class="info-label">Temperature</div>
  //         <div class="info-value">${batteryData.temperature.toFixed(2)} °C</div>
  //       </div>
  //       <div class="info-item">
  //         <div class="info-label">Cycle</div>
  //         <div class="info-value">${batteryData.cycle}</div>
  //       </div>
  //     </div>
  //     <button class="details-btn" onclick="showDiagnosticsBatteryDetails(${slotId})">Details</button>
  //   `;
  // } else {
  //   // 無電池的情況
  //   card.innerHTML = `
  //     <div class="battery-icon"></div>
  //     <div class="serial-number">No Battery</div>
  //     <div class="battery-percentage" style="color: #6b7280">--</div>
  //     <div class="battery-info-grid">
  //       <div class="info-item">
  //         <div class="info-label">Current</div>
  //         <div class="info-value">-- mA</div>
  //       </div>
  //       <div class="info-item">
  //         <div class="info-label">Voltage</div>
  //         <div class="info-value">-- mV</div>
  //       </div>
  //       <div class="info-item">
  //         <div class="info-label">Temperature</div>
  //         <div class="info-value">-- °C</div>
  //       </div>
  //       <div class="info-item">
  //         <div class="info-label">Cycle</div>
  //         <div class="info-value">--</div>
  //       </div>
  //     </div>
  //     <button class="details-btn" disabled style="opacity: 0.5; cursor: not-allowed;">No Data</button>
  //   `;
  // }
  const batteryHealth = getBatteryHealth(batteryData?.fullyChargedCapacity || 0, batteryData?.designCapacity || 0);
  if(isBatteryComplete(batteryData?.fullyChargedCapacity || 0, batteryData?.remainingCapacity || 0)) {
    let imgRotate = 0;
    let infoClass = "battery-info-top", snClass = "serial-number-top";
    if(slotId === 1 || slotId === 3 || slotId === 5) { 
      imgRotate = 0; 
      infoClass = "battery-info-top";
      snClass = "serial-number-top";
    }
    if(slotId === 0 || slotId === 2 || slotId === 4) { 
      imgRotate = 180; 
      infoClass = "battery-info-bottom";
      snClass = "serial-number-bottom";
    }
    
    // const resolvedPath = await resolveResource("src/main/assets/XL-Battery.png");
    // // const { convertFileSrc } = import('@tauri-apps/api/core');
    // const convertedSrc = await convertFileSrc(resolvedPath);
    // let imgsrc = convertedSrc;

    // const batteryLevel = batteryData?.relativeStateOfCharged || 0;
    // const current = batteryData?.current || 0;
    // if (current > 0) {
    //   // chargingCount++; // 正在充電
    //   imgsrc = await convertFileSrc(await resolveResource("src/main/assets/XL-Battery_charger.png"));
    // } else if (batteryLevel < 20) {
    //   //lowCount++; // 低電量
    //   imgsrc = await convertFileSrc(await resolveResource("src/main/assets/XL-Battery_low.png"));
    // } else {
    //   //normalCount++; // 正常
    //   imgsrc = await convertFileSrc(await resolveResource("src/main/assets/XL-Battery_normal.png"));
    // }

    
    //XL-Battery_charger 0
    //XL-Battery_low 0
    //XL-Battery_broken
    //XL-Battery_normal 0
//<img src="${imgsrc}" alt="Battery Image" class="battery-image image-rotate-${imgRotate}"/>
//<img src="src/main/assets/XL-Battery_normal.png" alt="Battery Image" class="battery-image image-rotate-0"/>

    card.innerHTML = `
      <div class="serial-number ${snClass}">${batteryData?.sn}</div>
      
      <div class="battery-info ${infoClass}" id="info${slotId}">
          <p>Current: ${batteryData?.current} mA</p>
          <p>Voltage: ${batteryData?.voltage} mV</p>
          <p>Temp: ${batteryData?.temperature.toFixed(2)} °C</p>
          <p>Health: ${batteryHealth.toFixed(2)}%</p>
          <p>Cycle: ${batteryData?.cycle}</p>
      </div>
      <button class="details-btn ${infoClass}" onclick="showDetails(${batteryData?.id})">Details</button>
    `;
  } else {
    card.innerHTML = `
      <div class="battery-info" id="info${slotId}">
          <p>No Battery</p>
      </div>
    `;
  }

  return card;
}

// // 顯示電池詳細資訊
// (window as any).showDiagnosticsBatteryDetails = function(slotId: number) {
//   const batteryData = currentBatteryData.find(battery => battery.id === slotId);
//   if (!batteryData || batteryData.sn === '') return;

//   const modal = document.getElementById('battery-details-modal');
//   const content = document.getElementById('battery-details-content');
  
//   if (!modal || !content) return;

//   content.innerHTML = `
//     <div class="details-grid">
//       <div class="detail-section">
//         <h4>基本資訊</h4>
//         <div class="detail-item">
//           <span class="detail-label">序號:</span>
//           <span class="detail-value">${batteryData.sn}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">設備名稱:</span>
//           <span class="detail-value">${batteryData.deviceName || 'N/A'}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">製造日期:</span>
//           <span class="detail-value">${batteryData.manufactureDate || 'N/A'}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">首次使用:</span>
//           <span class="detail-value">${batteryData.firstUseDate || 'N/A'}</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>電量資訊</h4>
//         <div class="detail-item">
//           <span class="detail-label">相對電量:</span>
//           <span class="detail-value">${batteryData.relativeStateOfCharged}%</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">絕對電量:</span>
//           <span class="detail-value">${batteryData.absoluteStateOfCharged}%</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">剩餘容量:</span>
//           <span class="detail-value">${batteryData.remainingCapacity} mAh</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">滿充容量:</span>
//           <span class="detail-value">${batteryData.fullyChargedCapacity} mAh</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">設計容量:</span>
//           <span class="detail-value">${batteryData.designCapacity} mAh</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>電壓電流</h4>
//         <div class="detail-item">
//           <span class="detail-label">電壓:</span>
//           <span class="detail-value">${batteryData.voltage} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">電流:</span>
//           <span class="detail-value">${batteryData.current} mA</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">平均電流:</span>
//           <span class="detail-value">${batteryData.averageCurrent} mA</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">充電電壓:</span>
//           <span class="detail-value">${batteryData.chargingVoltage} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">充電電流:</span>
//           <span class="detail-value">${batteryData.chargingCurrent} mA</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>溫度與循環</h4>
//         <div class="detail-item">
//           <span class="detail-label">溫度:</span>
//           <span class="detail-value">${batteryData.temperature.toFixed(2)} °C</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">循環次數:</span>
//           <span class="detail-value">${batteryData.cycle}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">循環指數:</span>
//           <span class="detail-value">${batteryData.cycleIndex}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">循環閾值:</span>
//           <span class="detail-value">${batteryData.cycleThreshold}</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>電芯電壓</h4>
//         <div class="detail-item">
//           <span class="detail-label">電芯1:</span>
//           <span class="detail-value">${batteryData.cellVoltage_1} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">電芯2:</span>
//           <span class="detail-value">${batteryData.cellVoltage_2} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">電芯3:</span>
//           <span class="detail-value">${batteryData.cellVoltage_3} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">電芯4:</span>
//           <span class="detail-value">${batteryData.cellVoltage_4} mV</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">總電壓:</span>
//           <span class="detail-value">${batteryData.packVoltage} mV</span>
//         </div>
//       </div>

//       <div class="detail-section">
//         <h4>狀態資訊</h4>
//         <div class="detail-item">
//           <span class="detail-label">運行狀態:</span>
//           <span class="detail-value">${batteryData.operationStatus}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">充電狀態:</span>
//           <span class="detail-value">${batteryData.chargingStatus}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">溫度範圍:</span>
//           <span class="detail-value">${batteryData.temperatureRange}</span>
//         </div>
//         <div class="detail-item">
//           <span class="detail-label">錯誤代碼:</span>
//           <span class="detail-value">${batteryData.error}</span>
//         </div>
//       </div>
//     </div>
//   `;

//   modal.classList.remove('hidden');
// };

// 更新統計資訊
function updateStatistics(batteryDataList: BatteryData[]) {
  const totalElement = document.getElementById("total-batteries");
  const normalElement = document.getElementById("normal-batteries");
  const incompleteElement = document.getElementById("incomplete-batteries");
  const errorElement = document.getElementById("error-batteries");

  // 過濾有效電池（有序號的電池）
  const validBatteries = batteryDataList.filter(battery => battery.sn !== '');
  
  if (totalElement) totalElement.textContent = validBatteries.length.toString();

  // 計算各種狀態的電池數量
  let normalCount = 0;
  let incompleteCount = 0;
  let errorCount = 0;

  validBatteries.forEach(battery => {
    //const batteryLevel = battery.relativeStateOfCharged || 0;
    //const current = battery.current || 0;
    const batteryHealth = getBatteryHealth(battery?.fullyChargedCapacity ?? 0, battery?.designCapacity ?? 0)

    if (isBatteryComplete(battery?.fullyChargedCapacity, battery?.remainingCapacity)) {
      console.log("completeCount ++");      
      console.log("batteryHealth: " + batteryHealth);
      console.log("isBatteryComplete: " + isBatteryComplete(battery?.fullyChargedCapacity, battery?.remainingCapacity));
      if (batteryHealth < 50) {
        errorCount++; // 電池不健康
      } else {
        normalCount++; // 正常電池
      }
    } else {
      console.log("incompleteCount ++");
      console.log("batteryHealth: " + batteryHealth);
      console.log("isBatteryComplete: " + isBatteryComplete(battery?.fullyChargedCapacity, battery?.remainingCapacity));
      incompleteCount++; // 資料錯誤
    }
  });

  if (normalElement) normalElement.textContent = normalCount.toString();
  if (incompleteElement) incompleteElement.textContent = incompleteCount.toString();
  if (errorElement) errorElement.textContent = errorCount.toString();
}

export async function get_batteries(){
  // 儀表板頁面的初始化邏輯
  try {
    if (await invoke("LOAD_SDK")) {
      listen("USB_ChangedEvent", async () => {
        Initial();
      });
      
      Initial();
      
      // 設置關閉詳細資訊彈窗的事件監聽器
      const closeBtn = document.getElementById('close-details-btn');
      const modal = document.getElementById('battery-details-modal');
      
      if (closeBtn && modal) {
        closeBtn.addEventListener('click', () => {
          modal.classList.add('hidden');
        });
      }

      // 點擊彈窗外部關閉
      if (modal) {
        modal.addEventListener('click', (e) => {
          if (e.target === modal) {
            modal.classList.add('hidden');
          }
        });
      }
    }
  }
  catch (error) {
    console.error("Failed to load library:", error);
  }
}

// 顯示電池詳細資訊
(window as any).showDetails = function(slotId: number) {
  //alert("showBatteryDetails");
  //alert("slotId: " + slotId);
  console.log("batteries: ", currentBatteryData);
  const batteryData = currentBatteryData.find(battery => battery.id === slotId);
  console.log("battery: ", batteryData);
  if (!batteryData || batteryData.sn === '') return;

  const modal = document.getElementById('battery-details-modal');
  const content = document.getElementById('battery-details-content');
  
  if (!modal || !content) return;

  content.innerHTML = `
    <div class="details-grid">
      <div class="detail-section">
        <h4>📦基本資訊</h4>
        <div class="detail-item">
          <span class="detail-label">ID:</span>
          <span class="detail-value">${batteryData.id}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">SN 序號:</span>
          <span class="detail-value">${batteryData.sn}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Battery Type 電池類型:</span>
          <span class="detail-value">${getBatteryType(batteryData.sn)}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PACK 電池包數量:</span>
          <span class="detail-value">${batteryData.pack}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">MODE 模式碼:</span>
          <span class="detail-value">${batteryData.mode}</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>🔋電池資訊與製造區塊</h4>
        <div class="detail-item">
          <span class="detail-label">Device Name 設備名稱:</span>
          <span class="detail-value">${batteryData.deviceName || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">GaugeStatus 電量計狀態碼:</span>
          <span class="detail-value">${batteryData.gaugeStatus}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Error 錯誤代碼:</span>
          <span class="detail-value">${batteryData.error}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureDate 製造日期:</span>
          <span class="detail-value">${batteryData.manufactureDate || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureBlock_1 製造資訊區塊1:</span>
          <span class="detail-value">${batteryData.manufactureBlock_1 || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureBlock_2 製造資訊區塊2:</span>
          <span class="detail-value">${batteryData.manufactureBlock_2 || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureBlock_3 製造資訊區塊3:</span>
          <span class="detail-value">${batteryData.manufactureBlock_3 || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ManufactureBlock_4 製造資訊區塊4:</span>
          <span class="detail-value">${batteryData.manufactureBlock_4 || 'N/A'}</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>📊電量資訊</h4>
        <div class="detail-item">
          <span class="detail-label">RelativeStateOfCharged 相對電量:</span>
          <span class="detail-value">${batteryData.relativeStateOfCharged} %</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">AbsoluteStateOfCharged 絕對電量:</span>
          <span class="detail-value">${batteryData.absoluteStateOfCharged} %</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">RemainingCapacity 剩餘容量:</span>
          <span class="detail-value">${batteryData.remainingCapacity} mAh</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedCapacity 滿充容量:</span>
          <span class="detail-value">${batteryData.fullyChargedCapacity} mAh</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">DesignVoltage 設計電壓:</span>
          <span class="detail-value">${batteryData.designVoltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">DesignCapacity 設計容量:</span>
          <span class="detail-value">${batteryData.designCapacity} mAh</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>⚡ 電壓電流</h4>
        <div class="detail-item">
          <span class="detail-label">Voltage 電壓:</span>
          <span class="detail-value">${batteryData.voltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Current 電流:</span>
          <span class="detail-value">${batteryData.current} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">AverageCurrent 平均電流:</span>
          <span class="detail-value">${batteryData.averageCurrent} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ChargingVoltage 充電電壓:</span>
          <span class="detail-value">${batteryData.chargingVoltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ChargingCurrent 充電電流:</span>
          <span class="detail-value">${batteryData.chargingCurrent} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">UntilFullyCharged 距離充滿所需時間:</span>
          <span class="detail-value">${batteryData.untilFullyCharged} min</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">UntilFullyDischarged 距離放完電時間:</span>
          <span class="detail-value">${batteryData.untilFullyDischarged} min</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>🌡️ 溫度與循環</h4>
        <div class="detail-item">
          <span class="detail-label">Temperature 溫度:</span>
          <span class="detail-value">${batteryData.temperature.toFixed(2)} °C</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">Cycle 循環次數:</span>
          <span class="detail-value">${batteryData.cycle}</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>🔄 延伸資料 (XXL)</h4>
        <div class="detail-item">
          <span class="detail-label">CycleIndex 循環指數:</span>
          <span class="detail-value">${batteryData.cycleIndex}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">CycleThreshold 循環閾值:</span>
          <span class="detail-value">${batteryData.cycleThreshold}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedDate 最近一次充滿日期:</span>
          <span class="detail-value">${batteryData.fullyChargedDate || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedCapacityThreshold 完充容量門檻值:</span>
          <span class="detail-value">${batteryData.fullyChargedCapacityThreshold}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedCapacityBackup 完充容量備份值:</span>
          <span class="detail-value">${batteryData.fullyChargedCapacityBackup}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">XXLLifetimeMaxPackVoltage 生命週期中最大整包電壓:</span>
          <span class="detail-value">${batteryData.xxlLifetimeMaxPackVoltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">XXLLifetimeMinPackVoltage 最小整包電壓:</span>
          <span class="detail-value">${batteryData.xxlLifetimeMinPackVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">XXLLifetimeMaxCurrent 最大電流:</span>
          <span class="detail-value">${batteryData.xxlLifetimeMaxCurrent} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">XXLLifetimeMinCurrent 最小電流:</span>
          <span class="detail-value">${batteryData.xxlLifetimeMinCurrent} mA</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OrangeLED 橘燈狀態:</span>
          <span class="detail-value">${batteryData.orangeLED}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FullyChargedVoltage 完全充電時電壓:</span>
          <span class="detail-value">${batteryData.fullyChargedVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FirstUseDate 首次使用日期:</span>
          <span class="detail-value">${batteryData.firstUseDate || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">RecordDate 紀錄日期:</span>
          <span class="detail-value">${batteryData.recordDate || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">RecordTime 紀錄時間:</span>
          <span class="detail-value">${batteryData.recordTime || 'N/A'}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PackMode 電池包模式:</span>
          <span class="detail-value">${batteryData.packMode}</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>🔋 電芯與電壓資訊</h4>
        <div class="detail-item">
          <span class="detail-label">CellVoltage_1 電芯1:</span>
          <span class="detail-value">${batteryData.cellVoltage_1} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">CellVoltage_2 電芯2:</span>
          <span class="detail-value">${batteryData.cellVoltage_2} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">CellVoltage_3 電芯3:</span>
          <span class="detail-value">${batteryData.cellVoltage_3} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">CellVoltage_4 電芯4:</span>
          <span class="detail-value">${batteryData.cellVoltage_4} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PackVoltage 電池包總電壓:</span>
          <span class="detail-value">${batteryData.packVoltage} mV</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">FETControl 開關控制狀態:</span>
          <span class="detail-value">${batteryData.fetControl}</span>
        </div>
      </div>

      <div class="detail-section">
        <h4>🛡️ 安全與錯誤狀態</h4>
        <div class="detail-item">
          <span class="detail-label">SafetyAlert_1 安全警報:</span>
          <span class="detail-value">${batteryData.safetyAlert_1}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">SafetyAlert_2 安全警報:</span>
          <span class="detail-value">${batteryData.safetyAlert_2}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">SafetyStatus_1 安全狀態:</span>
          <span class="detail-value">${batteryData.safetyStatus_1}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">SafetyStatus_2 安全狀態:</span>
          <span class="detail-value">${batteryData.safetyStatus_2}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PFAlert_1 永久失效警報:</span>
          <span class="detail-value">${batteryData.pfAlert_1}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PFAlert_2 永久失效警報:</span>
          <span class="detail-value">${batteryData.pfAlert_2}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PFStatus_1 永久失效狀態:</span>
          <span class="detail-value">${batteryData.pfStatus_1}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">PFStatus_2 永久失效狀態:</span>
          <span class="detail-value">${batteryData.pfStatus_2}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OperationStatus 運行狀態 操作狀態碼:</span>
          <span class="detail-value">${batteryData.operationStatus}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">ChargingStatus 充電狀態碼:</span>
          <span class="detail-value">${batteryData.chargingStatus}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">TemperatureRange 溫度範圍:</span>
          <span class="detail-value">${batteryData.temperatureRange}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">MaxError 最大誤差:</span>
          <span class="detail-value">${batteryData.maxError} %</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxTemperature 使用過程中最高溫度:</span>
          <span class="detail-value">${batteryData.lifetimeMaxTemperature} °C</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMinTemperature 最低溫度:</span>
          <span class="detail-value">${batteryData.lifetimeMinTemperature} °C</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeAvgTemperature 平均溫度:</span>
          <span class="detail-value">${batteryData.lifetimeAvgTemperature}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxCellVoltage 最大單顆電芯電壓:</span>
          <span class="detail-value">${batteryData.lifetimeMaxCellVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMinCellVoltage 最小單顆電芯電壓:</span>
          <span class="detail-value">${batteryData.lifetimeMinCellVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxPackVoltage 最大整包電壓:</span>
          <span class="detail-value">${batteryData.lifetimeMaxPackVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMinPackVoltage 最小整包電壓:</span>
          <span class="detail-value">${batteryData.lifetimeMinPackVoltage}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxChargingCurrent 最大充電電流:</span>
          <span class="detail-value">${batteryData.lifetimeMaxChargingCurrent}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxDischargingCurrent 最大放電電流:</span>
          <span class="detail-value">${batteryData.lifetimeMaxDischargingCurrent}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxAvgDischargingCurrent 最大平均放電電流:</span>
          <span class="detail-value">${batteryData.lifetimeMaxAvgDischargingCurrent}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxChargingPower 最大充電功率:</span>
          <span class="detail-value">${batteryData.lifetimeMaxChargingPower}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxDischargingPower 最大放電功率:</span>
          <span class="detail-value">${batteryData.lifetimeMaxDischargingPower}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeMaxAvgDischargingPower 最大平均放電功率:</span>
          <span class="detail-value">${batteryData.lifetimeMaxAvgDischargingPower}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">LifetimeTemperatureSamples 溫度樣本總數:</span>
          <span class="detail-value">${batteryData.lifetimeTemperatureSamples}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OTEventCount 過溫事件次數:</span>
          <span class="detail-value">${batteryData.otEventCount}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OTEventDuration 過溫事件總時長:</span>
          <span class="detail-value">${batteryData.otEventDuration}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OVEventCount 過壓事件次數:</span>
          <span class="detail-value">${batteryData.ovEventCount}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">OVEventDuration 過壓事件總時長:</span>
          <span class="detail-value">${batteryData.ovEventDuration}</span>
        </div>
        
      </div>
    </div>
  `;

  modal.classList.remove('hidden');
};


// (async function() {

// })();




// // 初始化控制器
// window.addEventListener('DOMContentLoaded', () => {
//   // 儀表板頁面的初始化邏輯
//   console.log("DOMContentLoaded");
  
// });

// window.addEventListener("DOMContentLoaded", async () => {
//   console.log("DOMContentLoaded async");
// });

// (function() {
//   // 儀表板頁面的初始化邏輯
//   console.log("初始化設定頁面");
//   const controller = new BatteryViewController();
//   controller.initialize();
// })();
